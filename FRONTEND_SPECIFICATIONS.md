# Frontend Sign-In Feature Specifications

## Overview
This document provides comprehensive specifications for implementing a sign-in feature with a posts and comments display page for your frontend application.

## 1. Sign-In Page Components

### 1.1 Sign-In Form Component
**File: `components/SignInForm.jsx` (or `.vue`, `.tsx` depending on your framework)**

**Features:**
- Email input field with validation
- Password input field with show/hide toggle
- "Remember Me" checkbox (optional)
- Sign-in button with loading state
- Link to sign-up page
- Error message display
- Form validation (client-side)

**Required Fields:**
```javascript
{
  email: string (required, email format),
  password: string (required, min 6 characters)
}
```

**API Integration:**
- Endpoint: `POST http://localhost:3000/users/signin`
- Request body: `{ email, password }`
- Success response: `{ token, user: { id, name, email, role } }`
- Error response: `{ error: "message" }`

### 1.2 Posts Feed Component
**File: `components/PostsFeed.jsx`**

**Features:**
- Display all posts in chronological order (newest first)
- Each post shows: title, content, author name, creation date
- Comments section for each post
- Responsive design for mobile and desktop
- Loading states and error handling
- Infinite scroll or pagination (optional)

## 2. Data Structure Specifications

### 2.1 Post Object Structure
```javascript
{
  id: number,
  title: string,
  content: string,
  createdAt: string (ISO date),
  updatedAt: string (ISO date),
  User: {
    id: number,
    name: string,
    email: string
  },
  Comments: [
    {
      id: number,
      content: string,
      createdAt: string (ISO date),
      updatedAt: string (ISO date),
      User: {
        id: number,
        name: string,
        email: string
      }
    }
  ]
}
```

### 2.2 Authentication State
```javascript
{
  isAuthenticated: boolean,
  user: {
    id: number,
    name: string,
    email: string,
    role: string
  } | null,
  token: string | null
}
```

## 3. Page Layout Specifications

### 3.1 Sign-In Page Layout
```
┌─────────────────────────────────────┐
│              Header                 │
│         (App Logo/Title)            │
├─────────────────────────────────────┤
│                                     │
│         Sign-In Form                │
│    ┌─────────────────────┐          │
│    │     Email Input     │          │
│    ├─────────────────────┤          │
│    │   Password Input    │          │
│    ├─────────────────────┤          │
│    │   [Sign In Button]  │          │
│    └─────────────────────┘          │
│                                     │
│    Don't have account? Sign Up      │
│                                     │
├─────────────────────────────────────┤
│              Posts Feed             │
│                                     │
│    ┌─────────────────────┐          │
│    │       Post 1        │          │
│    │   Title, Content    │          │
│    │   Author, Date      │          │
│    │                     │          │
│    │     Comments:       │          │
│    │   - Comment 1       │          │
│    │   - Comment 2       │          │
│    └─────────────────────┘          │
│                                     │
│    ┌─────────────────────┐          │
│    │       Post 2        │          │
│    │       ...           │          │
│    └─────────────────────┘          │
│                                     │
└─────────────────────────────────────┘
```

## 4. Component Specifications

### 4.1 PostCard Component
**Props:**
- `post`: Post object with user and comments
- `isAuthenticated`: boolean

**Features:**
- Display post title, content, author, and date
- Show all comments with author names and dates
- Responsive design
- Time formatting (e.g., "2 hours ago", "Yesterday")

### 4.2 CommentItem Component
**Props:**
- `comment`: Comment object with user info

**Features:**
- Display comment content
- Show commenter name and date
- Avatar placeholder or initials
- Responsive text formatting

## 5. State Management Requirements

### 5.1 Authentication State
- Store JWT token in localStorage/sessionStorage
- Manage user authentication status
- Handle token expiration
- Automatic logout on token expiry

### 5.2 Posts State
- Store fetched posts data
- Loading states for API calls
- Error handling for failed requests
- Cache management (optional)

## 6. API Integration Specifications

### 6.1 Authentication API
```javascript
// Sign In
const signIn = async (credentials) => {
  const response = await fetch('http://localhost:3000/users/signin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(credentials)
  });
  return response.json();
};
```

### 6.2 Posts API
```javascript
// Get All Posts with Details
const getAllPosts = async (token = null) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  const response = await fetch('http://localhost:3000/posts/all-with-details', {
    method: 'GET',
    headers
  });
  return response.json();
};
```

## 7. Styling Specifications

### 7.1 Design System
- **Colors:**
  - Primary: #3B82F6 (blue)
  - Secondary: #6B7280 (gray)
  - Success: #10B981 (green)
  - Error: #EF4444 (red)
  - Background: #F9FAFB (light gray)

- **Typography:**
  - Headings: Bold, larger font sizes
  - Body text: Regular weight, readable size
  - Timestamps: Smaller, muted color

- **Spacing:**
  - Consistent padding/margins (8px, 16px, 24px, 32px)
  - Card spacing: 16px padding, 8px margin

### 7.2 Responsive Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 8. User Experience Features

### 8.1 Loading States
- Skeleton loaders for posts while fetching
- Button loading spinners during sign-in
- Progressive loading for large comment lists

### 8.2 Error Handling
- Toast notifications for errors
- Inline form validation messages
- Retry mechanisms for failed API calls

### 8.3 Accessibility
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## 9. Security Considerations

### 9.1 Token Management
- Store JWT securely
- Include token in Authorization header
- Handle token refresh (if implemented)
- Clear token on logout

### 9.2 Input Validation
- Client-side form validation
- XSS prevention in user content
- CSRF protection (if needed)

## 10. Performance Optimizations

### 10.1 Data Loading
- Lazy loading for images
- Pagination or infinite scroll for large datasets
- Debounced search functionality (if added)

### 10.2 Caching
- Cache posts data temporarily
- Implement proper cache invalidation
- Use React Query or similar for data fetching (recommended)
