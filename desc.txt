📄 Project Description – Back-End Only
This back-end application is built with Node.js, Express.js, and Sequelize ORM using a MySQL database.
It provides a complete API for managing Users, Posts, and Comments in a relational database.

Main Purpose
The goal of this back-end is to handle all database operations and business logic for a simple blog-like system.
It allows creating, reading, updating, deleting, and searching data, while keeping relationships between users, posts, and comments.

Key Features
User Management

Create new users

View all users

Delete users by ID

Post Management

Create new posts with title, content, and owner user ID

View all posts with related user and comment information

Delete posts by ID

Comment Management

Create multiple comments in bulk

View the newest 3 comments for a specific post

Search comments by a specific word in their content

View detailed comment info with related user and post

Database Relationships

User → Post: One user can have many posts

Post → Comment: One post can have many comments

User → Comment: One user can have many comments

Technology Stack
Node.js – Server environment

Express.js – Web framework for building APIs

Sequelize – ORM for working with MySQL

MySQL – Relational database

ES6 Modules – Modern JavaScript syntax

How It Works
The application defines API endpoints for each operation (Users, Posts, Comments).

Sequelize is used to interact with the database using models and relationships.

API responses are sent in JSON format for the front-end to consume.

The database stores and organizes all user, post, and comment data with proper linking.

If you want, I can also prepare a shorter version for quick documentation use.
Do you want me to do that?