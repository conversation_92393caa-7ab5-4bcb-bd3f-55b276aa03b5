{"name": "sequelize", "version": "1.0.0", "main": "src/index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.3", "sequelize": "^6.37.7"}}