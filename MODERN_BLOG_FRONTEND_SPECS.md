# Modern Blog Application - Frontend Architecture Specifications

## 🎯 Project Overview
A professional, modern blog application with user authentication, content management, and admin dashboard. Built with React, featuring a clean design, responsive layout, and comprehensive functionality.

## 🏗️ Application Architecture

### Core Features
1. **Authentication System**
   - Sign Up / Sign In pages
   - JWT token management
   - Role-based access (User/Admin)
   - Protected routes

2. **User Dashboard**
   - View personal posts and comments
   - Create new posts
   - Add comments to posts
   - Edit/delete own content

3. **Admin Dashboard**
   - Manage all users, posts, and comments
   - View analytics and statistics
   - Delete any content
   - User role management

4. **Public Features**
   - Browse all posts (guest access)
   - Read comments
   - Responsive design

## 📱 Page Structure

### 1. Authentication Pages
- **`/signin`** - Sign In Form
- **`/signup`** - Sign Up Form

### 2. Public Pages
- **`/`** - Home page with all posts
- **`/posts/:id`** - Individual post view

### 3. User Dashboard
- **`/dashboard`** - User dashboard overview
- **`/dashboard/posts`** - My posts management
- **`/dashboard/comments`** - My comments management
- **`/dashboard/create-post`** - Create new post

### 4. Admin Dashboard
- **`/admin`** - Admin dashboard overview
- **`/admin/users`** - User management
- **`/admin/posts`** - All posts management
- **`/admin/comments`** - All comments management

## 🎨 Design System

### Color Palette
```css
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-500: #6b7280;
  --gray-700: #374151;
  --gray-900: #111827;
  
  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  
  /* Background */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
}
```

### Typography
```css
/* Font Families */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-mono: 'JetBrains Mono', 'Fira Code', monospace;

/* Font Sizes */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
--text-2xl: 1.5rem;
--text-3xl: 1.875rem;
--text-4xl: 2.25rem;
```

### Spacing System
```css
--space-1: 0.25rem;
--space-2: 0.5rem;
--space-3: 0.75rem;
--space-4: 1rem;
--space-6: 1.5rem;
--space-8: 2rem;
--space-12: 3rem;
--space-16: 4rem;
```

## 🧩 Component Architecture

### 1. Layout Components
```
src/
├── components/
│   ├── Layout/
│   │   ├── Header.jsx
│   │   ├── Sidebar.jsx
│   │   ├── Footer.jsx
│   │   └── Layout.jsx
│   ├── Navigation/
│   │   ├── Navbar.jsx
│   │   ├── UserMenu.jsx
│   │   └── AdminNav.jsx
```

### 2. Authentication Components
```
├── components/
│   ├── Auth/
│   │   ├── SignInForm.jsx
│   │   ├── SignUpForm.jsx
│   │   ├── ProtectedRoute.jsx
│   │   └── AdminRoute.jsx
```

### 3. Content Components
```
├── components/
│   ├── Posts/
│   │   ├── PostCard.jsx
│   │   ├── PostList.jsx
│   │   ├── PostForm.jsx
│   │   ├── PostDetail.jsx
│   │   └── PostActions.jsx
│   ├── Comments/
│   │   ├── CommentItem.jsx
│   │   ├── CommentList.jsx
│   │   ├── CommentForm.jsx
│   │   └── CommentActions.jsx
```

### 4. Dashboard Components
```
├── components/
│   ├── Dashboard/
│   │   ├── DashboardCard.jsx
│   │   ├── StatsCard.jsx
│   │   ├── UserDashboard.jsx
│   │   └── AdminDashboard.jsx
│   ├── Tables/
│   │   ├── DataTable.jsx
│   │   ├── UserTable.jsx
│   │   ├── PostTable.jsx
│   │   └── CommentTable.jsx
```

### 5. UI Components
```
├── components/
│   ├── UI/
│   │   ├── Button.jsx
│   │   ├── Input.jsx
│   │   ├── Modal.jsx
│   │   ├── Toast.jsx
│   │   ├── Loading.jsx
│   │   ├── Avatar.jsx
│   │   └── Badge.jsx
```

## 🔄 State Management

### Context Structure
```javascript
// contexts/
├── AuthContext.jsx      // Authentication state
├── PostsContext.jsx     // Posts data management
├── CommentsContext.jsx  // Comments data management
├── AdminContext.jsx     // Admin dashboard data
└── UIContext.jsx        // UI state (modals, toasts)
```

### State Shape
```javascript
// AuthContext
{
  user: {
    id: number,
    name: string,
    email: string,
    role: 'user' | 'admin'
  } | null,
  token: string | null,
  isAuthenticated: boolean,
  loading: boolean,
  error: string | null
}

// PostsContext
{
  posts: Post[],
  userPosts: Post[],
  currentPost: Post | null,
  loading: boolean,
  error: string | null
}

// AdminContext
{
  users: User[],
  allPosts: Post[],
  allComments: Comment[],
  stats: {
    totalUsers: number,
    totalPosts: number,
    totalComments: number
  },
  loading: boolean
}
```

## 🛣️ Routing Structure

```javascript
// App.jsx routing structure
<Routes>
  {/* Public Routes */}
  <Route path="/" element={<HomePage />} />
  <Route path="/signin" element={<SignInPage />} />
  <Route path="/signup" element={<SignUpPage />} />
  <Route path="/posts/:id" element={<PostDetailPage />} />
  
  {/* Protected User Routes */}
  <Route path="/dashboard" element={<ProtectedRoute><UserDashboard /></ProtectedRoute>}>
    <Route index element={<DashboardOverview />} />
    <Route path="posts" element={<MyPosts />} />
    <Route path="comments" element={<MyComments />} />
    <Route path="create-post" element={<CreatePost />} />
  </Route>
  
  {/* Admin Routes */}
  <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>}>
    <Route index element={<AdminOverview />} />
    <Route path="users" element={<UserManagement />} />
    <Route path="posts" element={<PostManagement />} />
    <Route path="comments" element={<CommentManagement />} />
  </Route>
</Routes>
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
--mobile: 320px;
--tablet: 768px;
--desktop: 1024px;
--wide: 1280px;

@media (min-width: 768px) { /* Tablet */ }
@media (min-width: 1024px) { /* Desktop */ }
@media (min-width: 1280px) { /* Wide */ }
```

### Layout Grid
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}
```

## 🔧 Development Tools

### Required Dependencies
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "axios": "^1.3.0",
    "date-fns": "^2.29.0",
    "react-hook-form": "^7.43.0",
    "react-hot-toast": "^2.4.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^3.1.0",
    "vite": "^4.1.0",
    "tailwindcss": "^3.2.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  }
}
```

### Build Configuration
- **Vite** for fast development and building
- **Tailwind CSS** for utility-first styling
- **ESLint + Prettier** for code quality
- **React Hook Form** for form management
- **Axios** for API calls
- **React Hot Toast** for notifications
