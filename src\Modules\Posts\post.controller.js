import * as postService from './Services/post.service.js';

export const create = async (req, res) => {
  try {
    const postData = {
      ...req.body,
      userId: req.user.userId
    };
    const post = await postService.createPost(postData);
    res.status(201).json(post);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const remove = async (req, res) => {
  try {
    const result = await postService.deletePost(req.params.postId, req.body.userId);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getDetails = async (req, res) => {
  try {
    const posts = await postService.getPostsDetails();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getCommentCount = async (req, res) => {
  try {
    const posts = await postService.getPostsCommentCount();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getAllWithDetails = async (req, res) => {
  try {
    const posts = await postService.getAllPostsWithDetails();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getUserPosts = async (req, res) => {
  try {
    const posts = await postService.getUserPosts(req.user.userId);
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const updatePost = async (req, res) => {
  try {
    const post = await postService.updatePost(req.params.postId, req.user.userId, req.body);
    res.json(post);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
