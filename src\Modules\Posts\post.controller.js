import * as postService from './Services/post.service.js';

export const create = async (req, res) => {
  try {
    const post = await postService.createPost(req.body);
    res.status(201).json(post);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const remove = async (req, res) => {
  try {
    const result = await postService.deletePost(req.params.postId, req.body.userId);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getDetails = async (req, res) => {
  try {
    const posts = await postService.getPostsDetails();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getCommentCount = async (req, res) => {
  try {
    const posts = await postService.getPostsCommentCount();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getAllWithDetails = async (req, res) => {
  try {
    const posts = await postService.getAllPostsWithDetails();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
