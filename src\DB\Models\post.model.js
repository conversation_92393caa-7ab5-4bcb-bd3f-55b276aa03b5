import { DataTypes, Model } from 'sequelize';
import sequelize from '../db.connection.js';

class Post extends Model {}

Post.init({
  id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
  title: { type: DataTypes.STRING, allowNull: false },
  content: { type: DataTypes.TEXT, allowNull: false },
  userId: { type: DataTypes.INTEGER, allowNull: false }
}, {
  sequelize,
  modelName: 'Post',
  paranoid: true
});

export default Post;
