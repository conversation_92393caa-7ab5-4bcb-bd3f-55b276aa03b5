import { Post, User, Comment } from '../../../DB/Models/index.js';

export const createPost = async (data) => {
  const post = Post.build(data);
  return await post.save();
};

export const deletePost = async (postId, userId) => {
  const post = await Post.findByPk(postId);
  if (!post) throw new Error('Post not found');
  if (post.userId !== userId) throw new Error('Not authorized');
  await post.destroy();
  return { message: 'Post deleted' };
};

export const getPostsDetails = async () => {
  return await Post.findAll({
    attributes: ['id', 'title'],
    include: [
      { model: User, attributes: ['id', 'name'] },
      { model: Comment, attributes: ['id', 'content'] }
    ]
  });
};

export const getPostsCommentCount = async () => {
  return await Post.findAll({
    attributes: ['id', 'title', [Post.sequelize.fn('COUNT', Post.sequelize.col('Comments.id')), 'commentCount']],
    include: [{ model: Comment, attributes: [] }],
    group: ['Post.id']
  });
};

export const getAllPostsWithDetails = async () => {
  return await Post.findAll({
    attributes: ['id', 'title', 'content', 'createdAt', 'updatedAt'],
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email']
      },
      {
        model: Comment,
        attributes: ['id', 'content', 'createdAt', 'updatedAt'],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'ASC']]
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

export const getUserPosts = async (userId) => {
  return await Post.findAll({
    where: { userId },
    attributes: ['id', 'title', 'content', 'createdAt', 'updatedAt'],
    include: [
      {
        model: User,
        attributes: ['id', 'name', 'email']
      },
      {
        model: Comment,
        attributes: ['id', 'content', 'createdAt', 'updatedAt'],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'ASC']]
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

export const updatePost = async (postId, userId, data) => {
  const post = await Post.findByPk(postId);
  if (!post) throw new Error('Post not found');
  if (post.userId !== userId) throw new Error('Not authorized to update this post');

  await post.update(data);
  return post;
};
