import { Sequelize } from 'sequelize';

const sequelize = new Sequelize('finalProject', 'root', 'omar', {
  host: 'localhost',
  dialect: 'mysql',
  logging: false
});


// (async () => {
//   try {
//     await sequelize.sync({ force: true }); 
//     console.log(" All tables have been cleared!");
//   } catch (error) {
//     console.error(" Error clearing database:", error);
//   } finally {
//     await sequelize.close();
//   }
// })();



export default sequelize;
