import * as userService from "./Services/user.service.js";

export const signup = async (req, res) => {
  try {
    const user = await userService.createUser(req.body);
    res.status(201).json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const signin = async (req, res) => {
  try {
    const result = await userService.authenticateUser(req.body);
    res.json(result);
  } catch (err) {
    res.status(401).json({ error: err.message });
  }
};

export const update = async (req, res) => {
  try {
    await userService.updateUser(req.params.id, req.body);
    res.json({ message: 'User updated' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getByEmail = async (req, res) => {
  try {
    const user = await userService.findByEmail(req.query.email);
    res.json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const getById = async (req, res) => {
  try {
    const user = await userService.getUserById(req.params.id);
    res.json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
