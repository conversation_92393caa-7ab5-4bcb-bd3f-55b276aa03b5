import * as adminService from './admin.service.js';

// User Management
export const getAllUsers = async (req, res) => {
  try {
    const users = await adminService.getAllUsers();
    res.json(users);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const deleteUser = async (req, res) => {
  try {
    const result = await adminService.deleteUser(req.params.userId);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const updateUserRole = async (req, res) => {
  try {
    const user = await adminService.updateUserRole(req.params.userId, req.body.role);
    res.json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Post Management
export const getAllPosts = async (req, res) => {
  try {
    const posts = await adminService.getAllPostsAdmin();
    res.json(posts);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const deletePost = async (req, res) => {
  try {
    const result = await adminService.deletePostAdmin(req.params.postId);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Comment Management
export const getAllComments = async (req, res) => {
  try {
    const comments = await adminService.getAllCommentsAdmin();
    res.json(comments);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const deleteComment = async (req, res) => {
  try {
    const result = await adminService.deleteCommentAdmin(req.params.commentId);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Dashboard
export const getDashboardStats = async (req, res) => {
  try {
    const stats = await adminService.getDashboardStats();
    res.json(stats);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
