// src/Modules/Comments/Services/comment.service.js
import { Comment, Post, User } from '../../../DB/Models/index.js';
import { Op } from 'sequelize';


export const bulkCreateComments = async (comments = []) => {
  if (!Array.isArray(comments) || comments.length === 0) {
    return [];
  }
  const payload = comments.map(c => ({
    content: String(c.content ?? '').trim(),
    postId: Number(c.postId),
    userId: Number(c.userId),
  }));
  return await Comment.bulkCreate(payload);
};


export const updateComment = async (commentId, userId, content) => {
  const id = Number(commentId);
  const uid = Number(userId);

  const comment = await Comment.findByPk(id);
  if (!comment) throw new Error('Comment not found');
  if (comment.userId !== uid) throw new Error('Not authorized');

  comment.content = String(content ?? '').trim();
  return await comment.save();
};


export const findOrCreateComment = async (data) => {
  const payload = {
    postId: Number(data.postId),
    userId: Number(data.userId),
    content: String(data.content ?? '').trim(),
  };

  const [comment] = await Comment.findOrCreate({
    where: payload,
    defaults: payload,
  });
  return comment;
};


export const searchComments = async (word = '') => {
  const q = String(word).trim();
  if (!q) return { count: 0, rows: [] };

  return await Comment.findAndCountAll({
    where: { content: { [Op.like]: `%${q}%` } }, // ✅ المهم هنا
    order: [['createdAt', 'DESC']],
  });
};


export const getNewestComments = async (postId) => {
  const pid = Number(postId);
  return await Comment.findAll({
    where: { postId: pid },
    order: [['createdAt', 'DESC']],
    limit: 3,
  });
};


export const getCommentDetails = async (id) => {
  const cid = Number(id);
  return await Comment.findByPk(cid, {
    include: [
      { model: User, attributes: ['id', 'name', 'email'] },
      { model: Post, attributes: ['id', 'title'] },
    ],
  });
};
