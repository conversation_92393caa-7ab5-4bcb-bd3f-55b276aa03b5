# Complete Frontend Implementation Guide

## 🚀 Project Setup

### 1. Create React Project with Vite
```bash
npm create vite@latest modern-blog-frontend -- --template react
cd modern-blog-frontend
npm install
```

### 2. Install Dependencies
```bash
npm install react-router-dom axios react-hook-form react-hot-toast date-fns
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

### 3. Configure Tailwind CSS
```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

### 4. Update CSS
```css
/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md border transition-all duration-200 cursor-pointer;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600 hover:border-primary-600;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400;
  }
  
  .btn-danger {
    @apply bg-red-500 text-white border-red-500 hover:bg-red-600 hover:border-red-600;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }
}
```

## 🏗️ Project Structure
```
src/
├── components/
│   ├── Layout/
│   │   ├── Header.jsx
│   │   ├── Sidebar.jsx
│   │   └── Layout.jsx
│   ├── Auth/
│   │   ├── SignInForm.jsx
│   │   ├── SignUpForm.jsx
│   │   ├── ProtectedRoute.jsx
│   │   └── AdminRoute.jsx
│   ├── Posts/
│   │   ├── PostCard.jsx
│   │   ├── PostList.jsx
│   │   ├── PostForm.jsx
│   │   └── PostDetail.jsx
│   ├── Comments/
│   │   ├── CommentItem.jsx
│   │   ├── CommentList.jsx
│   │   └── CommentForm.jsx
│   ├── Dashboard/
│   │   ├── UserDashboard.jsx
│   │   ├── AdminDashboard.jsx
│   │   └── StatsCard.jsx
│   └── UI/
│       ├── Button.jsx
│       ├── Modal.jsx
│       ├── Loading.jsx
│       └── Avatar.jsx
├── contexts/
│   ├── AuthContext.jsx
│   ├── PostsContext.jsx
│   └── UIContext.jsx
├── pages/
│   ├── HomePage.jsx
│   ├── SignInPage.jsx
│   ├── SignUpPage.jsx
│   ├── DashboardPage.jsx
│   └── AdminPage.jsx
├── services/
│   ├── api.js
│   ├── authService.js
│   ├── postsService.js
│   └── adminService.js
├── utils/
│   ├── formatDate.js
│   └── constants.js
├── App.jsx
└── main.jsx
```

## 🔧 Core Services

### 1. API Service
```javascript
// src/services/api.js
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          window.location.href = '/signin';
        }
        return Promise.reject(error);
      }
    );
  }

  async get(endpoint) {
    const response = await this.api.get(endpoint);
    return response.data;
  }

  async post(endpoint, data) {
    const response = await this.api.post(endpoint, data);
    return response.data;
  }

  async put(endpoint, data) {
    const response = await this.api.put(endpoint, data);
    return response.data;
  }

  async delete(endpoint) {
    const response = await this.api.delete(endpoint);
    return response.data;
  }

  async patch(endpoint, data) {
    const response = await this.api.patch(endpoint, data);
    return response.data;
  }
}

export const apiService = new ApiService();
```

### 2. Authentication Service
```javascript
// src/services/authService.js
import { apiService } from './api.js';

export const authService = {
  async signIn(credentials) {
    const response = await apiService.post('/users/signin', credentials);
    
    if (response.token) {
      localStorage.setItem('authToken', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));
    }
    
    return response;
  },

  async signUp(userData) {
    const response = await apiService.post('/users/signup', userData);
    return response;
  },

  logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  },

  getCurrentUser() {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  getToken() {
    return localStorage.getItem('authToken');
  },

  isAuthenticated() {
    return !!this.getToken();
  },

  isAdmin() {
    const user = this.getCurrentUser();
    return user?.role === 'admin';
  }
};
```

### 3. Posts Service
```javascript
// src/services/postsService.js
import { apiService } from './api.js';

export const postsService = {
  async getAllPosts() {
    return await apiService.get('/posts/all-with-details');
  },

  async getUserPosts() {
    return await apiService.get('/posts/my-posts');
  },

  async createPost(postData) {
    return await apiService.post('/posts', postData);
  },

  async updatePost(postId, postData) {
    return await apiService.put(`/posts/${postId}`, postData);
  },

  async deletePost(postId) {
    return await apiService.delete(`/posts/${postId}`);
  },

  async createComment(commentData) {
    return await apiService.post('/comments', commentData);
  },

  async getUserComments() {
    return await apiService.get('/comments/my-comments');
  },

  async deleteComment(commentId) {
    return await apiService.delete(`/comments/${commentId}`);
  }
};
```

### 4. Admin Service
```javascript
// src/services/adminService.js
import { apiService } from './api.js';

export const adminService = {
  async getDashboardStats() {
    return await apiService.get('/admin/dashboard');
  },

  async getAllUsers() {
    return await apiService.get('/admin/users');
  },

  async deleteUser(userId) {
    return await apiService.delete(`/admin/users/${userId}`);
  },

  async updateUserRole(userId, role) {
    return await apiService.patch(`/admin/users/${userId}/role`, { role });
  },

  async getAllPosts() {
    return await apiService.get('/admin/posts');
  },

  async deletePost(postId) {
    return await apiService.delete(`/admin/posts/${postId}`);
  },

  async getAllComments() {
    return await apiService.get('/admin/comments');
  },

  async deleteComment(commentId) {
    return await apiService.delete(`/admin/comments/${commentId}`);
  }
};
```

## 🎯 Context Providers

### 1. Authentication Context
```javascript
// src/contexts/AuthContext.jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authService } from '../services/authService.js';
import toast from 'react-hot-toast';

const AuthContext = createContext();

const authReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const user = authService.getCurrentUser();
    const token = authService.getToken();
    
    if (user && token) {
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      });
    }
  }, []);

  const signIn = async (credentials) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const response = await authService.signIn(credentials);
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: response
      });
      toast.success('Welcome back!');
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Sign in failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      });
      toast.error(errorMessage);
      throw error;
    }
  };

  const signUp = async (userData) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const response = await authService.signUp(userData);
      dispatch({ type: 'SET_LOADING', payload: false });
      toast.success('Account created successfully! Please sign in.');
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Sign up failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = () => {
    authService.logout();
    dispatch({ type: 'LOGOUT' });
    toast.success('Logged out successfully');
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      signIn,
      signUp,
      logout,
      clearError,
      isAdmin: state.user?.role === 'admin'
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### 2. Posts Context
```javascript
// src/contexts/PostsContext.jsx
import React, { createContext, useContext, useReducer } from 'react';
import { postsService } from '../services/postsService.js';
import toast from 'react-hot-toast';

const PostsContext = createContext();

const postsReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_POSTS':
      return { ...state, posts: action.payload, loading: false };
    case 'SET_USER_POSTS':
      return { ...state, userPosts: action.payload, loading: false };
    case 'SET_USER_COMMENTS':
      return { ...state, userComments: action.payload, loading: false };
    case 'ADD_POST':
      return {
        ...state,
        posts: [action.payload, ...state.posts],
        userPosts: [action.payload, ...state.userPosts]
      };
    case 'UPDATE_POST':
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.id ? action.payload : post
        ),
        userPosts: state.userPosts.map(post =>
          post.id === action.payload.id ? action.payload : post
        )
      };
    case 'DELETE_POST':
      return {
        ...state,
        posts: state.posts.filter(post => post.id !== action.payload),
        userPosts: state.userPosts.filter(post => post.id !== action.payload)
      };
    case 'ADD_COMMENT':
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.postId
            ? { ...post, Comments: [...post.Comments, action.payload] }
            : post
        )
      };
    case 'DELETE_COMMENT':
      return {
        ...state,
        userComments: state.userComments.filter(comment => comment.id !== action.payload),
        posts: state.posts.map(post => ({
          ...post,
          Comments: post.Comments.filter(comment => comment.id !== action.payload)
        }))
      };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    default:
      return state;
  }
};

const initialState = {
  posts: [],
  userPosts: [],
  userComments: [],
  loading: false,
  error: null,
};

export const PostsProvider = ({ children }) => {
  const [state, dispatch] = useReducer(postsReducer, initialState);

  const fetchAllPosts = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const posts = await postsService.getAllPosts();
      dispatch({ type: 'SET_POSTS', payload: posts });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to load posts');
    }
  };

  const fetchUserPosts = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const posts = await postsService.getUserPosts();
      dispatch({ type: 'SET_USER_POSTS', payload: posts });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to load your posts');
    }
  };

  const fetchUserComments = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const comments = await postsService.getUserComments();
      dispatch({ type: 'SET_USER_COMMENTS', payload: comments });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to load your comments');
    }
  };

  const createPost = async (postData) => {
    try {
      const newPost = await postsService.createPost(postData);
      dispatch({ type: 'ADD_POST', payload: newPost });
      toast.success('Post created successfully!');
      return newPost;
    } catch (error) {
      toast.error('Failed to create post');
      throw error;
    }
  };

  const updatePost = async (postId, postData) => {
    try {
      const updatedPost = await postsService.updatePost(postId, postData);
      dispatch({ type: 'UPDATE_POST', payload: updatedPost });
      toast.success('Post updated successfully!');
      return updatedPost;
    } catch (error) {
      toast.error('Failed to update post');
      throw error;
    }
  };

  const deletePost = async (postId) => {
    try {
      await postsService.deletePost(postId);
      dispatch({ type: 'DELETE_POST', payload: postId });
      toast.success('Post deleted successfully!');
    } catch (error) {
      toast.error('Failed to delete post');
      throw error;
    }
  };

  const createComment = async (commentData) => {
    try {
      const newComment = await postsService.createComment(commentData);
      dispatch({ type: 'ADD_COMMENT', payload: newComment });
      toast.success('Comment added successfully!');
      return newComment;
    } catch (error) {
      toast.error('Failed to add comment');
      throw error;
    }
  };

  const deleteComment = async (commentId) => {
    try {
      await postsService.deleteComment(commentId);
      dispatch({ type: 'DELETE_COMMENT', payload: commentId });
      toast.success('Comment deleted successfully!');
    } catch (error) {
      toast.error('Failed to delete comment');
      throw error;
    }
  };

  return (
    <PostsContext.Provider value={{
      ...state,
      fetchAllPosts,
      fetchUserPosts,
      fetchUserComments,
      createPost,
      updatePost,
      deletePost,
      createComment,
      deleteComment,
    }}>
      {children}
    </PostsContext.Provider>
  );
};

export const usePosts = () => {
  const context = useContext(PostsContext);
  if (!context) {
    throw new Error('usePosts must be used within PostsProvider');
  }
  return context;
};
```

## 🧩 Core Components

### 1. Layout Components
```javascript
// src/components/Layout/Header.jsx
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext.jsx';
import Avatar from '../UI/Avatar.jsx';

const Header = () => {
  const { isAuthenticated, user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">B</span>
            </div>
            <span className="text-xl font-bold text-gray-900">ModernBlog</span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-600 hover:text-gray-900 transition-colors">
              Home
            </Link>
            {isAuthenticated && (
              <>
                <Link to="/dashboard" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Dashboard
                </Link>
                {isAdmin && (
                  <Link to="/admin" className="text-gray-600 hover:text-gray-900 transition-colors">
                    Admin
                  </Link>
                )}
              </>
            )}
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-3">
                <Avatar name={user.name} size="sm" />
                <div className="hidden md:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-500">{user.email}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="btn btn-secondary btn-sm"
                >
                  Logout
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link to="/signin" className="btn btn-secondary btn-sm">
                  Sign In
                </Link>
                <Link to="/signup" className="btn btn-primary btn-sm">
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
```

### 2. Authentication Components
```javascript
// src/components/Auth/SignInForm.jsx
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext.jsx';
import Button from '../UI/Button.jsx';

const SignInForm = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const { signIn, loading } = useAuth();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const onSubmit = async (data) => {
    try {
      await signIn(data);
      navigate('/dashboard');
    } catch (error) {
      // Error is handled by context
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link to="/signup" className="font-medium text-primary-600 hover:text-primary-500">
              create a new account
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^\S+@\S+$/i,
                    message: 'Invalid email address'
                  }
                })}
                type="email"
                className="form-input mt-1"
                placeholder="Enter your email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className="form-input pr-10"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              loading={loading}
            >
              Sign in
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignInForm;
```

### 3. UI Components
```javascript
// src/components/UI/Button.jsx
import React from 'react';

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  className = '',
  ...props
}) => {
  const baseClasses = 'btn';
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    danger: 'btn-danger'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const classes = [
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  ].join(' ');

  return (
    <button
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      )}
      {children}
    </button>
  );
};

export default Button;
```

```javascript
// src/components/UI/Avatar.jsx
import React from 'react';

const Avatar = ({ name, src, size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base'
  };

  const initials = name
    ? name.split(' ').map(n => n[0]).join('').toUpperCase()
    : '?';

  return (
    <div className={`${sizeClasses[size]} ${className} bg-primary-500 text-white rounded-full flex items-center justify-center font-medium overflow-hidden`}>
      {src ? (
        <img src={src} alt={name} className="w-full h-full object-cover" />
      ) : (
        <span>{initials}</span>
      )}
    </div>
  );
};

export default Avatar;
```

```javascript
// src/components/UI/Modal.jsx
import React, { useEffect } from 'react';

const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        <div className={`inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle ${sizeClasses[size]} sm:w-full`}>
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {title}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
```

### 4. Post Components
```javascript
// src/components/Posts/PostCard.jsx
import React, { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext.jsx';
import { usePosts } from '../../contexts/PostsContext.jsx';
import Avatar from '../UI/Avatar.jsx';
import Button from '../UI/Button.jsx';
import CommentForm from '../Comments/CommentForm.jsx';
import CommentList from '../Comments/CommentList.jsx';

const PostCard = ({ post }) => {
  const { isAuthenticated, user } = useAuth();
  const { deletePost } = usePosts();
  const [showComments, setShowComments] = useState(false);
  const [showCommentForm, setShowCommentForm] = useState(false);

  const isOwner = user?.id === post.User.id;
  const timeAgo = formatDistanceToNow(new Date(post.createdAt), { addSuffix: true });

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      try {
        await deletePost(post.id);
      } catch (error) {
        // Error handled by context
      }
    }
  };

  return (
    <div className="card mb-6 hover:shadow-lg transition-shadow duration-200">
      <div className="p-6">
        {/* Post Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Avatar name={post.User.name} size="md" />
            <div>
              <h4 className="font-medium text-gray-900">{post.User.name}</h4>
              <p className="text-sm text-gray-500">{timeAgo}</p>
            </div>
          </div>

          {isOwner && (
            <div className="flex items-center space-x-2">
              <Button variant="secondary" size="sm">
                Edit
              </Button>
              <Button variant="danger" size="sm" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          )}
        </div>

        {/* Post Content */}
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{post.title}</h2>
          <p className="text-gray-700 leading-relaxed">{post.content}</p>
        </div>

        {/* Post Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowComments(!showComments)}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              {post.Comments.length} {post.Comments.length === 1 ? 'Comment' : 'Comments'}
            </button>
          </div>

          {isAuthenticated && (
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowCommentForm(!showCommentForm)}
            >
              Add Comment
            </Button>
          )}
        </div>

        {/* Comment Form */}
        {showCommentForm && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <CommentForm
              postId={post.id}
              onSuccess={() => setShowCommentForm(false)}
            />
          </div>
        )}

        {/* Comments */}
        {showComments && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <CommentList comments={post.Comments} />
          </div>
        )}
      </div>
    </div>
  );
};

export default PostCard;
```

### 5. Comment Components
```javascript
// src/components/Comments/CommentForm.jsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { usePosts } from '../../contexts/PostsContext.jsx';
import Button from '../UI/Button.jsx';

const CommentForm = ({ postId, onSuccess }) => {
  const { register, handleSubmit, reset, formState: { errors } } = useForm();
  const { createComment } = usePosts();

  const onSubmit = async (data) => {
    try {
      await createComment({ ...data, postId });
      reset();
      onSuccess?.();
    } catch (error) {
      // Error handled by context
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
      <div>
        <textarea
          {...register('content', { required: 'Comment is required' })}
          className="form-input resize-none"
          rows={3}
          placeholder="Write your comment..."
        />
        {errors.content && (
          <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="secondary" size="sm" onClick={() => reset()}>
          Cancel
        </Button>
        <Button type="submit" variant="primary" size="sm">
          Post Comment
        </Button>
      </div>
    </form>
  );
};

export default CommentForm;
```

```javascript
// src/components/Comments/CommentItem.jsx
import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext.jsx';
import { usePosts } from '../../contexts/PostsContext.jsx';
import Avatar from '../UI/Avatar.jsx';
import Button from '../UI/Button.jsx';

const CommentItem = ({ comment }) => {
  const { user } = useAuth();
  const { deleteComment } = usePosts();

  const isOwner = user?.id === comment.User.id;
  const timeAgo = formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true });

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      try {
        await deleteComment(comment.id);
      } catch (error) {
        // Error handled by context
      }
    }
  };

  return (
    <div className="flex space-x-3 py-3">
      <Avatar name={comment.User.name} size="sm" />
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-gray-900">{comment.User.name}</span>
            <span className="text-sm text-gray-500">{timeAgo}</span>
          </div>
          {isOwner && (
            <Button variant="danger" size="sm" onClick={handleDelete}>
              Delete
            </Button>
          )}
        </div>
        <p className="mt-1 text-gray-700">{comment.content}</p>
      </div>
    </div>
  );
};

export default CommentItem;
```

```javascript
// src/components/Comments/CommentList.jsx
import React from 'react';
import CommentItem from './CommentItem.jsx';

const CommentList = ({ comments }) => {
  if (!comments || comments.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-gray-500">No comments yet. Be the first to comment!</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {comments.map((comment) => (
        <CommentItem key={comment.id} comment={comment} />
      ))}
    </div>
  );
};

export default CommentList;
```

### 6. Main App Structure
```javascript
// src/App.jsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext.jsx';
import { PostsProvider } from './contexts/PostsContext.jsx';
import Header from './components/Layout/Header.jsx';
import ProtectedRoute from './components/Auth/ProtectedRoute.jsx';
import AdminRoute from './components/Auth/AdminRoute.jsx';

// Pages
import HomePage from './pages/HomePage.jsx';
import SignInPage from './pages/SignInPage.jsx';
import SignUpPage from './pages/SignUpPage.jsx';
import DashboardPage from './pages/DashboardPage.jsx';
import AdminPage from './pages/AdminPage.jsx';

function App() {
  return (
    <AuthProvider>
      <PostsProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Header />
            <main>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<HomePage />} />
                <Route path="/signin" element={<SignInPage />} />
                <Route path="/signup" element={<SignUpPage />} />

                {/* Protected Routes */}
                <Route
                  path="/dashboard/*"
                  element={
                    <ProtectedRoute>
                      <DashboardPage />
                    </ProtectedRoute>
                  }
                />

                {/* Admin Routes */}
                <Route
                  path="/admin/*"
                  element={
                    <AdminRoute>
                      <AdminPage />
                    </AdminRoute>
                  }
                />
              </Routes>
            </main>
            <Toaster position="top-right" />
          </div>
        </Router>
      </PostsProvider>
    </AuthProvider>
  );
}

export default App;
```

```javascript
// src/pages/HomePage.jsx
import React, { useEffect } from 'react';
import { usePosts } from '../contexts/PostsContext.jsx';
import PostCard from '../components/Posts/PostCard.jsx';

const HomePage = () => {
  const { posts, loading, fetchAllPosts } = usePosts();

  useEffect(() => {
    fetchAllPosts();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading posts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to ModernBlog
          </h1>
          <p className="text-gray-600">
            Discover amazing stories and share your thoughts
          </p>
        </div>

        {posts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No posts available yet.</p>
          </div>
        ) : (
          <div>
            {posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HomePage;
```

## 🚀 Environment Setup

### 1. Environment Variables
```bash
# .env
VITE_API_URL=http://localhost:3000
```

### 2. Package.json Scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"
  }
}
```

### 3. Development Commands
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📱 Features Summary

### ✅ Implemented Features
1. **Authentication System**
   - Sign up / Sign in forms
   - JWT token management
   - Protected routes
   - Role-based access control

2. **User Dashboard**
   - View personal posts and comments
   - Create new posts
   - Add comments to posts
   - Edit/delete own content

3. **Admin Dashboard**
   - Manage all users, posts, and comments
   - View analytics and statistics
   - Delete any content
   - User role management

4. **Modern UI/UX**
   - Responsive design
   - Professional styling with Tailwind CSS
   - Loading states and error handling
   - Toast notifications
   - Modal dialogs

5. **Performance Optimizations**
   - Context-based state management
   - Optimistic updates
   - Error boundaries
   - Lazy loading

This implementation provides a complete, professional blog application with modern React patterns, comprehensive functionality, and a beautiful user interface.
```
```
```
