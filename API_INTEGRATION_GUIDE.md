# Frontend API Integration Guide

## Overview
This guide provides complete code examples and implementation details for integrating your frontend application with the enhanced backend API.

## 1. API Service Layer

### 1.1 Base API Configuration
```javascript
// services/api.js
const API_BASE_URL = 'http://localhost:3000';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'API request failed');
      }
      
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  setAuthToken(token) {
    this.token = token;
  }

  getAuthHeaders() {
    return this.token ? { Authorization: `Bearer ${this.token}` } : {};
  }
}

export const apiService = new ApiService();
```

### 1.2 Authentication Service
```javascript
// services/authService.js
import { apiService } from './api.js';

export const authService = {
  async signIn(credentials) {
    const response = await apiService.request('/users/signin', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    // Store token
    if (response.token) {
      localStorage.setItem('authToken', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));
      apiService.setAuthToken(response.token);
    }
    
    return response;
  },

  async signUp(userData) {
    const response = await apiService.request('/users/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    return response;
  },

  logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    apiService.setAuthToken(null);
  },

  getCurrentUser() {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  getToken() {
    return localStorage.getItem('authToken');
  },

  isAuthenticated() {
    return !!this.getToken();
  },

  initializeAuth() {
    const token = this.getToken();
    if (token) {
      apiService.setAuthToken(token);
    }
  }
};
```

### 1.3 Posts Service
```javascript
// services/postsService.js
import { apiService } from './api.js';

export const postsService = {
  async getAllPostsWithDetails() {
    return await apiService.request('/posts/all-with-details', {
      method: 'GET',
      headers: apiService.getAuthHeaders(),
    });
  },

  async createPost(postData) {
    return await apiService.request('/posts', {
      method: 'POST',
      headers: apiService.getAuthHeaders(),
      body: JSON.stringify(postData),
    });
  },

  async deletePost(postId, userId) {
    return await apiService.request(`/posts/${postId}`, {
      method: 'DELETE',
      headers: apiService.getAuthHeaders(),
      body: JSON.stringify({ userId }),
    });
  },

  async getPostDetails() {
    return await apiService.request('/posts/details', {
      method: 'GET',
      headers: apiService.getAuthHeaders(),
    });
  }
};
```

## 2. React Implementation Examples

### 2.1 Authentication Context
```javascript
// contexts/AuthContext.jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authService } from '../services/authService.js';

const AuthContext = createContext();

const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };
    default:
      return state;
  }
};

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Initialize auth on app start
    authService.initializeAuth();
    const user = authService.getCurrentUser();
    const token = authService.getToken();
    
    if (user && token) {
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      });
    }
  }, []);

  const signIn = async (credentials) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const response = await authService.signIn(credentials);
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: response
      });
      return response;
    } catch (error) {
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: error.message
      });
      throw error;
    }
  };

  const logout = () => {
    authService.logout();
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      signIn,
      logout,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### 2.2 Sign-In Form Component
```javascript
// components/SignInForm.jsx
import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext.jsx';

export const SignInForm = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, loading, error } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await signIn(formData);
      // Success - user will be redirected by auth state change
    } catch (error) {
      // Error is handled by context
      console.error('Sign in failed:', error);
    }
  };

  const isFormValid = formData.email && formData.password.length >= 6;

  return (
    <div className="sign-in-form">
      <h2>Sign In</h2>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            disabled={loading}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="password">Password</label>
          <div className="password-input">
            <input
              type={showPassword ? 'text' : 'password'}
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              minLength={6}
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="password-toggle"
            >
              {showPassword ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>
        </div>
        
        <button
          type="submit"
          disabled={!isFormValid || loading}
          className="sign-in-button"
        >
          {loading ? 'Signing In...' : 'Sign In'}
        </button>
      </form>
      
      <p className="sign-up-link">
        Don't have an account? <a href="/signup">Sign Up</a>
      </p>
    </div>
  );
};
```

### 2.3 Posts Feed Component
```javascript
// components/PostsFeed.jsx
import React, { useState, useEffect } from 'react';
import { postsService } from '../services/postsService.js';
import { PostCard } from './PostCard.jsx';
import { useAuth } from '../contexts/AuthContext.jsx';

export const PostsFeed = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const postsData = await postsService.getAllPostsWithDetails();
      setPosts(postsData);
      setError(null);
    } catch (err) {
      setError('Failed to load posts');
      console.error('Error fetching posts:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="posts-feed">
        <div className="loading">Loading posts...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="posts-feed">
        <div className="error">
          {error}
          <button onClick={fetchPosts}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="posts-feed">
      <h2>Latest Posts</h2>
      {posts.length === 0 ? (
        <div className="no-posts">No posts available</div>
      ) : (
        <div className="posts-list">
          {posts.map(post => (
            <PostCard
              key={post.id}
              post={post}
              isAuthenticated={isAuthenticated}
            />
          ))}
        </div>
      )}
    </div>
  );
};
```

### 2.4 Post Card Component
```javascript
// components/PostCard.jsx
import React from 'react';
import { CommentItem } from './CommentItem.jsx';

export const PostCard = ({ post, isAuthenticated }) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <div className="post-card">
      <div className="post-header">
        <div className="post-author">
          <div className="author-avatar">
            {post.User.name.charAt(0).toUpperCase()}
          </div>
          <div className="author-info">
            <h3 className="author-name">{post.User.name}</h3>
            <span className="post-date">{formatDate(post.createdAt)}</span>
          </div>
        </div>
      </div>

      <div className="post-content">
        <h2 className="post-title">{post.title}</h2>
        <p className="post-text">{post.content}</p>
      </div>

      <div className="post-comments">
        <h4 className="comments-header">
          Comments ({post.Comments.length})
        </h4>

        {post.Comments.length === 0 ? (
          <p className="no-comments">No comments yet</p>
        ) : (
          <div className="comments-list">
            {post.Comments.map(comment => (
              <CommentItem
                key={comment.id}
                comment={comment}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
```

### 2.5 Comment Item Component
```javascript
// components/CommentItem.jsx
import React from 'react';

export const CommentItem = ({ comment }) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <div className="comment-item">
      <div className="comment-author">
        <div className="comment-avatar">
          {comment.User.name.charAt(0).toUpperCase()}
        </div>
        <div className="comment-info">
          <span className="comment-author-name">{comment.User.name}</span>
          <span className="comment-date">{formatDate(comment.createdAt)}</span>
        </div>
      </div>
      <p className="comment-content">{comment.content}</p>
    </div>
  );
};
```

## 3. Main App Integration

### 3.1 App Component
```javascript
// App.jsx
import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx';
import { SignInForm } from './components/SignInForm.jsx';
import { PostsFeed } from './components/PostsFeed.jsx';
import './App.css';

const AppContent = () => {
  const { isAuthenticated, user, logout } = useAuth();

  return (
    <div className="app">
      <header className="app-header">
        <h1>My Blog App</h1>
        {isAuthenticated && (
          <div className="user-info">
            <span>Welcome, {user.name}!</span>
            <button onClick={logout} className="logout-button">
              Logout
            </button>
          </div>
        )}
      </header>

      <main className="app-main">
        {!isAuthenticated && (
          <div className="sign-in-section">
            <SignInForm />
          </div>
        )}

        <div className="posts-section">
          <PostsFeed />
        </div>
      </main>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
```

## 4. CSS Styling Examples

### 4.1 Basic Styles
```css
/* App.css */
.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 30px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logout-button {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.sign-in-form {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
}

.sign-in-button {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
}

.sign-in-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.post-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.post-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.post-title {
  margin: 0 0 10px 0;
  color: #1f2937;
}

.post-text {
  color: #4b5563;
  line-height: 1.6;
}

.comment-item {
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.comment-author {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.comment-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #6b7280;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
}
```

## 5. Installation Requirements

Add these dependencies to your frontend project:

```bash
# For React projects
npm install

# No additional dependencies needed for basic implementation
# Optional: for better state management
npm install @tanstack/react-query

# For form validation (optional)
npm install react-hook-form
```

## 6. Environment Configuration

Create a `.env` file in your frontend project:

```env
REACT_APP_API_URL=http://localhost:3000
```

This comprehensive guide provides everything needed to implement the sign-in feature with posts and comments display in your frontend application.
```
