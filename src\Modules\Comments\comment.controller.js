import * as commentService from './Services/comment.service.js';

export const bulkCreate = async (req, res) => {
  try {
    const comments = await commentService.bulkCreateComments(req.body);
    res.status(201).json(comments);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const update = async (req, res) => {
  try {
    const comment = await commentService.updateComment(req.params.commentId, req.body.userId, req.body.content);
    res.json(comment);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const findOrCreate = async (req, res) => {
  try {
    const comment = await commentService.findOrCreateComment(req.body);
    res.json(comment);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const search = async (req, res) => {
  try {
    const result = await commentService.searchComments(req.query.word);
    res.json(result);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const newest = async (req, res) => {
  try {
    const comments = await commentService.getNewestComments(req.params.postId);
    res.json(comments);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

export const details = async (req, res) => {
  try {
    const comment = await commentService.getCommentDetails(req.params.id);
    res.json(comment);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
