import express from 'express';
import bodyParser from 'body-parser';

import * as userController from './Modules/Users/<USER>';
import * as postController from './Modules/Posts/post.controller.js';
import * as commentController from './Modules/Comments/comment.controller.js';
import { optionalAuth } from './Middleware/auth.middleware.js';
import cors from 'cors';

import sequelize from './DB/db.connection.js';

const app = express();
app.use(bodyParser.json());


// Allow requests from your frontend
app.use(cors({
  origin: 'http://localhost:5173', // your frontend URL
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));


// User routes
app.post('/users/signup', userController.signup);
app.post('/users/signin', userController.signin);
app.put('/users/:id', userController.update);
app.get('/users/by-email', userController.getByEmail);
app.get('/user/:id', userController.getById);

// Post routes
app.post('/posts', postController.create);
app.delete('/posts/:postId', postController.remove);
app.get('/posts/details', postController.getDetails);
app.get('/posts/comment-count', postController.getCommentCount);
app.get('/posts/all-with-details', optionalAuth, postController.getAllWithDetails);

// Comment routes
app.post('/comments', commentController.bulkCreate);
app.patch('/comments/:commentId', commentController.update);
app.post('/comments/find-or-create', commentController.findOrCreate);
app.get('/comments/search', commentController.search);
app.get('/comments/newest/:postId', commentController.newest);
app.get('/comments/details/:id', commentController.details);

sequelize.sync({ alter: true })
  .then(() => console.log('Database synced'))
  .catch(err => console.error(err));

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
