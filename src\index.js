import express from 'express';
import bodyParser from 'body-parser';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import * as userController from './Modules/Users/<USER>';
import * as postController from './Modules/Posts/post.controller.js';
import * as commentController from './Modules/Comments/comment.controller.js';
import * as adminController from './Modules/Admin/admin.controller.js';
import { optionalAuth, authenticateToken, requireAdmin } from './Middleware/auth.middleware.js';
import cors from 'cors';

import sequelize from './DB/db.connection.js';

const app = express();
app.use(bodyParser.json());


// Allow requests from your frontend
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    process.env.FRONTEND_URL || 'http://localhost:5174'
  ],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));


// User routes
app.post('/users/signup', userController.signup);
app.post('/users/signin', userController.signin);
app.put('/users/:id', userController.update);
app.get('/users/by-email', userController.getByEmail);
app.get('/user/:id', userController.getById);

// Post routes
app.post('/posts', authenticateToken, postController.create);
app.put('/posts/:postId', authenticateToken, postController.updatePost);
app.delete('/posts/:postId', postController.remove);
app.get('/posts/details', postController.getDetails);
app.get('/posts/comment-count', postController.getCommentCount);
app.get('/posts/all-with-details', optionalAuth, postController.getAllWithDetails);
app.get('/posts/my-posts', authenticateToken, postController.getUserPosts);

// Comment routes
app.post('/comments', authenticateToken, commentController.create);
app.post('/comments/bulk', commentController.bulkCreate);
app.patch('/comments/:commentId', commentController.update);
app.delete('/comments/:commentId', authenticateToken, commentController.deleteComment);
app.post('/comments/find-or-create', commentController.findOrCreate);
app.get('/comments/search', commentController.search);
app.get('/comments/newest/:postId', commentController.newest);
app.get('/comments/details/:id', commentController.details);
app.get('/comments/my-comments', authenticateToken, commentController.getUserComments);

// Admin routes
app.get('/admin/dashboard', authenticateToken, requireAdmin, adminController.getDashboardStats);
app.get('/admin/users', authenticateToken, requireAdmin, adminController.getAllUsers);
app.delete('/admin/users/:userId', authenticateToken, requireAdmin, adminController.deleteUser);
app.patch('/admin/users/:userId/role', authenticateToken, requireAdmin, adminController.updateUserRole);
app.get('/admin/posts', authenticateToken, requireAdmin, adminController.getAllPosts);
app.delete('/admin/posts/:postId', authenticateToken, requireAdmin, adminController.deletePost);
app.get('/admin/comments', authenticateToken, requireAdmin, adminController.getAllComments);
app.delete('/admin/comments/:commentId', authenticateToken, requireAdmin, adminController.deleteComment);

sequelize.sync({ alter: true })
  .then(() => console.log('Database synced'))
  .catch(err => console.error(err));

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});
