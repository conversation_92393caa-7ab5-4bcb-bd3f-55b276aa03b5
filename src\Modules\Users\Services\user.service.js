import { User } from '../../../DB/Models/index.js';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

export const createUser = async (data) => {
  const existing = await User.findOne({ where: { email: data.email } });
  if (existing) throw new Error('Email already exists');

  // Hash password before saving
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(data.password, saltRounds);

  const user = User.build({
    ...data,
    password: hashedPassword
  });
  return await user.save();
};

export const updateUser = async (id, data) => {
  return await User.update(data, { where: { id }, validate: false });
};

export const findByEmail = async (email) => {
  return await User.findOne({ where: { email } });
};

export const getUserById = async (id) => {
  return await User.findByPk(id, { attributes: { exclude: ['role'] } });
};

export const authenticateUser = async ({ email, password }) => {
  // Find user by email
  const user = await User.findOne({ where: { email } });
  if (!user) throw new Error('Invalid email or password');

  // Check password
  const isValidPassword = await bcrypt.compare(password, user.password);
  if (!isValidPassword) throw new Error('Invalid email or password');

  // Generate JWT token
  const token = jwt.sign(
    {
      userId: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );

  return {
    token,
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    }
  };
};
