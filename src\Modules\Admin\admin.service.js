import { User, Post, Comment } from '../../DB/Models/index.js';

// User Management
export const getAllUsers = async () => {
  return await User.findAll({
    attributes: ['id', 'name', 'email', 'role', 'createdAt', 'updatedAt'],
    order: [['createdAt', 'DESC']]
  });
};

export const deleteUser = async (userId) => {
  const user = await User.findByPk(userId);
  if (!user) throw new Error('User not found');
  if (user.role === 'admin') throw new Error('Cannot delete admin user');
  
  // Delete user's posts and comments first (cascade)
  await Comment.destroy({ where: { userId } });
  await Post.destroy({ where: { userId } });
  await user.destroy();
  
  return { message: 'User deleted successfully' };
};

export const updateUserRole = async (userId, role) => {
  const user = await User.findByPk(userId);
  if (!user) throw new Error('User not found');
  
  await user.update({ role });
  return user;
};

// Post Management
export const getAllPostsAdmin = async () => {
  return await Post.findAll({
    attributes: ['id', 'title', 'content', 'createdAt', 'updatedAt'],
    include: [
      { 
        model: User, 
        attributes: ['id', 'name', 'email', 'role'] 
      },
      { 
        model: Comment, 
        attributes: ['id', 'content', 'createdAt'],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'email']
          }
        ]
      }
    ],
    order: [['createdAt', 'DESC']]
  });
};

export const deletePostAdmin = async (postId) => {
  const post = await Post.findByPk(postId);
  if (!post) throw new Error('Post not found');
  
  // Delete associated comments first
  await Comment.destroy({ where: { postId } });
  await post.destroy();
  
  return { message: 'Post deleted successfully' };
};

// Comment Management
export const getAllCommentsAdmin = async () => {
  return await Comment.findAll({
    include: [
      { model: User, attributes: ['id', 'name', 'email'] },
      { model: Post, attributes: ['id', 'title'] },
    ],
    order: [['createdAt', 'DESC']]
  });
};

export const deleteCommentAdmin = async (commentId) => {
  const comment = await Comment.findByPk(commentId);
  if (!comment) throw new Error('Comment not found');
  
  await comment.destroy();
  return { message: 'Comment deleted successfully' };
};

// Dashboard Statistics
export const getDashboardStats = async () => {
  const totalUsers = await User.count();
  const totalPosts = await Post.count();
  const totalComments = await Comment.count();
  const adminUsers = await User.count({ where: { role: 'admin' } });
  
  return {
    totalUsers,
    totalPosts,
    totalComments,
    adminUsers,
    regularUsers: totalUsers - adminUsers
  };
};
